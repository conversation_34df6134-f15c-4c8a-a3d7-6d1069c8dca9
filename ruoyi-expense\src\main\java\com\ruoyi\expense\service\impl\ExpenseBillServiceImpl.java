package com.ruoyi.expense.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.LinkedHashMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.ruoyi.common.core.text.Convert;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.expense.domain.BillReceiveDTO;
import com.ruoyi.expense.domain.BillReceiveDTO.BillData;
import com.ruoyi.expense.domain.BillReceiveDTO.BillDetail;
import com.ruoyi.expense.domain.BillReceiveDTO.BillMetadata;
import com.ruoyi.expense.domain.BillReceiveDTO.ExpenseDetail;
import com.ruoyi.expense.domain.ExpenseBill;
import com.ruoyi.expense.domain.ExpenseDetails;
import com.ruoyi.expense.mapper.ExpenseBillMapper;
import com.ruoyi.expense.mapper.ExpenseDetailsMapper;
import com.ruoyi.expense.service.IExpenseBillService;
import com.ruoyi.expense.service.IExpenseBillNotificationService;
import com.ruoyi.framework.web.service.DictService;
import com.ruoyi.common.core.domain.entity.SysDictData;
import org.apache.ibatis.annotations.Param;

// 导入导出相关包
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import java.io.IOException;
import java.io.File;
import java.io.FileOutputStream;
import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.file.FileUtils;
import org.springframework.http.MediaType;
import java.util.HashMap;
import javax.servlet.http.HttpServletResponse;

// 导入用户相关包
import com.ruoyi.common.utils.ShiroUtils;
import com.ruoyi.common.core.domain.entity.SysUser;

/**
 * 账单数据Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-03-19
 */
@Service
public class ExpenseBillServiceImpl implements IExpenseBillService {
    private static final Logger log = LoggerFactory.getLogger(ExpenseBillServiceImpl.class);

    @Autowired
    private ExpenseBillMapper expenseBillMapper;

    @Autowired
    private ExpenseDetailsMapper expenseDetailsMapper;

    @Autowired
    private DictService dictService;
    
    @Autowired
    private IExpenseBillNotificationService notificationService;

    @Autowired
    private com.ruoyi.expense.service.IExpenseDetailsService expenseDetailsService;

    @Autowired
    private com.ruoyi.expense.mapper.ExpenseAllocationTableMapper expenseAllocationTableMapper;

    // 费用类型字典
    private static final String EXPENSE_TYPE_DICT = "expense_type";

    // 账单状态 - 已入库
    private static final String STATUS_IMPORTED = "0";

    // 账单状态 - 已退回
    private static final String STATUS_RETURNED = "3";

    /**
     * 查询账单数据
     * 
     * @param id 账单数据主键
     * @return 账单数据
     */
    @Override
    public ExpenseBill selectExpenseBillById(Long id) {
        return expenseBillMapper.selectExpenseBillById(id);
    }

    /**
     * 根据费用类型、计费周期和划账部门查询账单
     * 
     * @param expenseType        费用类型
     * @param billingCycle       计费周期
     * @param transferDepartment 划账部门
     * @return 账单数据
     */
    @Override
    public ExpenseBill selectExpenseBillByUniqueKey(
            @Param("expense_type") String expenseType,
            @Param("billing_cycle") String billingCycle,
            @Param("transfer_department") String transferDepartment) {
        return expenseBillMapper.selectExpenseBillByUniqueKey(expenseType, billingCycle, transferDepartment);
    }

    /**
     * 查询账单数据列表
     * 
     * @param expenseBill 账单数据
     * @return 账单数据
     */
    @Override
    public List<ExpenseBill> selectExpenseBillList(ExpenseBill expenseBill) {
        return expenseBillMapper.selectExpenseBillList(expenseBill);
    }

    /**
     * 新增账单数据
     * 
     * @param expenseBill 账单数据
     * @return 结果
     */
    @Override
    public int insertExpenseBill(ExpenseBill expenseBill) {
        expenseBill.setCreateTime(new Date());
        return expenseBillMapper.insertExpenseBill(expenseBill);
    }

    /**
     * 修改账单数据
     * 
     * @param expenseBill 账单数据
     * @return 结果
     */
    @Override
    public int updateExpenseBill(ExpenseBill expenseBill) {
        expenseBill.setUpdateTime(new Date());
        return expenseBillMapper.updateExpenseBill(expenseBill);
    }

    /**
     * 批量删除账单数据
     * 
     * @param ids 需要删除的账单数据主键
     * @return 结果
     */
    @Override
    public int deleteExpenseBillByIds(String ids) {
        return expenseBillMapper.deleteExpenseBillByIds(Convert.toStrArray(ids));
    }

    /**
     * 删除账单数据信息
     * 
     * @param id 账单数据主键
     * @return 结果
     */
    @Override
    public int deleteExpenseBillById(Long id) {
        return expenseBillMapper.deleteExpenseBillById(id);
    }

    /**
     * 接收并处理账单数据
     * 
     * @param receiveDTO 接收的账单数据
     * @return 处理结果消息
     */
    @Override
    @Transactional
    public String receiveBillData(BillReceiveDTO receiveDTO) {
        log.debug("开始处理账单数据：{}", receiveDTO);

        // 1. 账单数量是否符合
        if (receiveDTO == null || receiveDTO.getMetadata() == null || receiveDTO.getBills() == null) {
            log.error("账单元数据格式错误：数据为空或缺少必要字段");
            return "账单元数据格式错误：数据为空或缺少必要字段";
        }

        // Integer expectedCount = receiveDTO.getMetadata().getBillCount();
        // int actualCount = receiveDTO.getBills().size();
        // if (expectedCount == null || expectedCount != actualCount) {
        //     String message = String.format("账单数量不符，应发账单数量为%d，实际发送账单数量为%d",
        //             expectedCount == null ? 0 : expectedCount, actualCount);
        //     log.error(message);
        //     return message;
        // }

        // log.debug("账单数量校验通过，开始校验每一份账单");

        // 2. 遍历每一份账单
        Map<String, BillDetail> bills = receiveDTO.getBills();
        List<ExpenseBill> validBills = new ArrayList<>();
        List<List<ExpenseDetails>> validExpenseDetails = new ArrayList<>();

        // 先对账单ID进行排序，确保按账单1、账单2的顺序处理
        List<String> sortedBillKeys = new ArrayList<>(bills.keySet());
        sortedBillKeys.sort((k1, k2) -> {
            // 提取数字部分进行比较，如"账单1"中的1
            try {
                String num1 = k1.replaceAll("[^0-9]", "");
                String num2 = k2.replaceAll("[^0-9]", "");
                if (num1.length() > 0 && num2.length() > 0) {
                    return Integer.parseInt(num1) - Integer.parseInt(num2);
                }
            } catch (Exception e) {
                log.warn("账单ID排序异常", e);
            }
            // 默认按字符串排序
            return k1.compareTo(k2);
        });

        // 遍历排序后的账单
        int billIndex = 0;
        for (String billKey : sortedBillKeys) {
            billIndex++;
            BillDetail billDetail = bills.get(billKey);
            BillData billData = billDetail.getBillData();

            String department = billData.getDepartment();

            String departmentLabel = dictService.getLabel("expense_institude", department);

            log.debug("开始校验第{}份账单：{}", billIndex, billKey);

            // 2.1 校验当前账单中的JSON结构是否与BillReceiveDTO的结构一致
            if (billDetail == null || billDetail.getBillData() == null) {
                String message = String.format("%s([%s])数据格式错误，缺少必要字段。", billKey, departmentLabel);
                log.error(message);
                return message;
            }
            if (!validateBillDetailStructure(billDetail, billKey)) {
                String message = String.format("%s([%s])的JSON结构与数据接收模块定义的数据格式不一致，请检查数据格式", billKey, departmentLabel);
                log.error(message);
                return message;
            }

            // 2.1.5 校验账单元数据字段是否为空
            BillMetadata metadata = receiveDTO.getMetadata();
            if (metadata.getBillCount() == null) {
                String message = "账单元数据中的账单数量不能为空";
                log.error(message);
                return message;
            }
            if (StringUtils.isEmpty(metadata.getPublisher())) {
                String message = "账单元数据中的发布人不能为空";
                log.error(message);
                return message;
            }

            // 2.1.6 校验账单相关数据字段是否为空
            if (StringUtils.isEmpty(billData.getExpenseType())) {
                String message = String.format("%s([%s])的费用类型不能为空", billKey, departmentLabel);
                log.error(message);
                return message;
            }
            if (StringUtils.isEmpty(billData.getBillingCycle())) {
                String message = String.format("%s([%s])的计费周期不能为空", billKey, departmentLabel);
                log.error(message);
                return message;
            }
            if (StringUtils.isEmpty(billData.getDepartment())) {
                String message = String.format("%s([%s])的划账部门不能为空", billKey, departmentLabel);
                log.error(message);
                return message;
            }
            if (StringUtils.isEmpty(billData.getTaxIncludingTotal())) {
                String message = String.format("%s([%s])的含税总价不能为空", billKey, departmentLabel);
                log.error(message);
                return message;
            }
            if (StringUtils.isEmpty(billData.getTaxExcludingTotal())) {
                String message = String.format("%s([%s])的不含税总价不能为空", billKey, departmentLabel);
                log.error(message);
                return message;
            }

            // 2.1.8 校验账单金额字段的小数点位数
            String taxIncludingTotalError = validateDecimalPlaces(billData.getTaxIncludingTotal(), 
                String.format("%s([%s])的含税总价", billKey, departmentLabel));
            if (taxIncludingTotalError != null) {
                log.error(taxIncludingTotalError);
                return taxIncludingTotalError;
            }
            
            String taxExcludingTotalError = validateDecimalPlaces(billData.getTaxExcludingTotal(), 
                String.format("%s([%s])的不含税总价", billKey, departmentLabel));
            if (taxExcludingTotalError != null) {
                log.error(taxExcludingTotalError);
                return taxExcludingTotalError;
            }

            // 2.1.7 校验费用明细必填字段是否为空
            List<ExpenseDetail> details = billDetail.getExpenseDetails();
            if (details != null && !details.isEmpty()) {
                for (int i = 0; i < details.size(); i++) {
                    ExpenseDetail detail = details.get(i);
                    String detailPrefix = String.format("%s([%s])的第%d行费用明细", billKey, departmentLabel, i + 1);
                    
                    if (StringUtils.isEmpty(detail.getExpenseName())) {
                        String message = String.format("%s的费用名称不能为空", detailPrefix);
                        log.error(message);
                        return message;
                    }
                    if (StringUtils.isEmpty(detail.getBrand())) {
                        String message = String.format("%s的品牌不能为空", detailPrefix);
                        log.error(message);
                        return message;
                    }
                    if (detail.getQuantity() == null) {
                        String message = String.format("%s的数量不能为空", detailPrefix);
                        log.error(message);
                        return message;
                    }
                    if (detail.getTaxIncludingPrice() == null) {
                        String message = String.format("%s的含税单价不能为空", detailPrefix);
                        log.error(message);
                        return message;
                    }
                    if (detail.getTaxExcludingPrice() == null) {
                        String message = String.format("%s的不含税单价不能为空", detailPrefix);
                        log.error(message);
                        return message;
                    }
                    if (detail.getTaxIncludingLineTotal() == null) {
                        String message = String.format("%s的含税单行总价不能为空", detailPrefix);
                        log.error(message);
                        return message;
                    }
                    if (detail.getTaxExcludingLineTotal() == null) {
                        String message = String.format("%s的不含税单行总价不能为空", detailPrefix);
                        log.error(message);
                        return message;
                    }

                    // 校验费用明细价格字段的小数点位数
                    String taxIncludingPriceError = validateDecimalPlaces(detail.getTaxIncludingPrice(), 
                        String.format("%s的含税单价", detailPrefix));
                    if (taxIncludingPriceError != null) {
                        log.error(taxIncludingPriceError);
                        return taxIncludingPriceError;
                    }
                    
                    String taxExcludingPriceError = validateDecimalPlaces(detail.getTaxExcludingPrice(), 
                        String.format("%s的不含税单价", detailPrefix));
                    if (taxExcludingPriceError != null) {
                        log.error(taxExcludingPriceError);
                        return taxExcludingPriceError;
                    }
                    
                    String taxIncludingLineTotalError = validateDecimalPlaces(detail.getTaxIncludingLineTotal(), 
                        String.format("%s的含税单行总价", detailPrefix));
                    if (taxIncludingLineTotalError != null) {
                        log.error(taxIncludingLineTotalError);
                        return taxIncludingLineTotalError;
                    }
                    
                    String taxExcludingLineTotalError = validateDecimalPlaces(detail.getTaxExcludingLineTotal(), 
                        String.format("%s的不含税单行总价", detailPrefix));
                    if (taxExcludingLineTotalError != null) {
                        log.error(taxExcludingLineTotalError);
                        return taxExcludingLineTotalError;
                    }
                }
            }

            // 2.2 校验费用类型是否为EXPENSE_TYPE_DICT字典中的值
            String expenseType = billData.getExpenseType();
            if (StringUtils.isEmpty(expenseType) ||
                    dictService.getLabel(EXPENSE_TYPE_DICT, expenseType) == null) {
                String message = String.format("%s([%s])的费用类型(%s)有误，不存在该种费用类库", billKey, departmentLabel, expenseType);
                log.error(message);
                return message;
            }

            // 2.3 校验计费周期是否为类似这种日期格式：202503、202504-06
            String billingCycle = billData.getBillingCycle();
            if (StringUtils.isEmpty(billingCycle) || !isValidBillingCycle(billingCycle)) {
                String message = String.format("%s([%s])的计费周期格式(%s)有误，不符合类似202503或202501-03格式", billKey,
                        departmentLabel, billingCycle);
                log.error(message);
                return message;
            }

            // 2.4 校验划账部门
            if (StringUtils.isEmpty(department)) {
                String message = String.format("%s的(%s)划账部门不能为空", billKey, departmentLabel);
                log.error(message);
                return message;
            }

            // 获取部门名称
            if (StringUtils.isEmpty(departmentLabel)) {
                String message = String.format("%s([%s])的划账部门[%s]不在系统的部门字典中，请检查", billKey, departmentLabel, department);
                log.error(message);
                return message;
            }

            // 2.5 校验账单是否已存在且状态不为已退回
            log.info("检查重复账单：expenseType={}, billingCycle={}, department={}", expenseType, billingCycle, department);
            ExpenseBill existingBill = selectExpenseBillByUniqueKey(expenseType, billingCycle, department);
            
            if (existingBill != null) {
                log.info("发现已存在账单：bill_id={}, status={}, publisher={}", 
                    existingBill.getBill_id(), existingBill.getStatus(), existingBill.getBill_publisher());
                
                if (!STATUS_RETURNED.equals(existingBill.getStatus())) {
                    String message = String.format("账单%s(%s)已在系统中存在，且账单不为已退回状态，因此无法重新发布。请联系系统管理员或者对应部门核对人退回账单。",
                            billKey, departmentLabel);
                    log.error("重复账单检查失败：{}", message);
                    return message;
                } else {
                    log.info("账单{}({})已存在但状态为已退回，允许重新发布", billKey, departmentLabel);
                }
            } else {
                log.info("账单{}({})不存在，可以新增", billKey, departmentLabel);
            }

            // 2.6 校对账单费用：累加每一条费用明细的金额，与账单数据中的总价比较
            if (details == null || details.isEmpty()) {
                String message = String.format("%s([%s])没有费用明细数据", billKey, departmentLabel);
                log.error(message);
                return message;
            }

            log.debug("开始校验账单{}的费用明细，共{}条", billKey, details.size());

            List<ExpenseDetails> validDetails = new ArrayList<>();
            BigDecimal actualTaxIncludingTotal = BigDecimal.ZERO;
            BigDecimal actualTaxExcludingTotal = BigDecimal.ZERO;

            for (int i = 0; i < details.size(); i++) {
                ExpenseDetail detail = details.get(i);

                // 校验费用明细字段是否存在和类型是否正确
                try {
                    ExpenseDetails expenseDetails = convertToExpenseDetails(detail, expenseType, billingCycle,
                            department);
                    validDetails.add(expenseDetails);

                    // 累加金额
                    if (detail.getTaxIncludingLineTotal() != null) {
                        actualTaxIncludingTotal = actualTaxIncludingTotal.add(detail.getTaxIncludingLineTotal());
                    }
                    if (detail.getTaxExcludingLineTotal() != null) {
                        actualTaxExcludingTotal = actualTaxExcludingTotal.add(detail.getTaxExcludingLineTotal());
                    }

                    log.debug("账单{}的第{}条费用明细校验通过", billKey, i + 1);
                } catch (Exception e) {
                    String message = String.format("%s([%s])的第%d行的费用明细有误，请检查字段顺序、值的类型", billKey, departmentLabel,
                            i + 1);
                    log.error(message, e);
                    return message;
                }
            }

            // 校验账单总金额
            BigDecimal billTaxIncludingTotal = null;
            BigDecimal billTaxExcludingTotal = null;

            try {
                if (billData.getTaxIncludingTotal() != null) {
                    billTaxIncludingTotal = new BigDecimal(billData.getTaxIncludingTotal());
                }
                if (billData.getTaxExcludingTotal() != null) {
                    billTaxExcludingTotal = new BigDecimal(billData.getTaxExcludingTotal());
                }
            } catch (NumberFormatException e) {
                String message = String.format("%s([%s])的总价格式错误，无法转换为数字", billKey, departmentLabel);
                log.error(message);
                return message;
            }

            // 与实际计算的金额比较 - 使用容差值避免精度问题
            final BigDecimal TOLERANCE = new BigDecimal("0.02"); // 2分钱的容差
            
            BigDecimal taxInclusiveDifference = BigDecimal.ZERO;
            BigDecimal taxExclusiveDifference = BigDecimal.ZERO;
            
            // 检查含税总价
            if (billTaxIncludingTotal != null) {
                taxInclusiveDifference = actualTaxIncludingTotal.subtract(billTaxIncludingTotal).abs();
            }
            
            // 检查不含税总价
            if (billTaxExcludingTotal != null) {
                taxExclusiveDifference = actualTaxExcludingTotal.subtract(billTaxExcludingTotal).abs();
            }
            
            // 使用容差比较
            if ((billTaxIncludingTotal != null && taxInclusiveDifference.compareTo(TOLERANCE) > 0) ||
                    (billTaxExcludingTotal != null && taxExclusiveDifference.compareTo(TOLERANCE) > 0)) {
                String message = String.format("%s([%s])的含税总价为%s，系统计算后的实际含税总价为%s（差异：%s）。%s(%s)的不含税总价为%s，系统计算后的实际不含税总价为%s（差异：%s）。请修改！",
                        billKey, departmentLabel, billData.getTaxIncludingTotal(), actualTaxIncludingTotal.toString(), taxInclusiveDifference.toString(),
                        billKey, departmentLabel, billData.getTaxExcludingTotal(), actualTaxExcludingTotal.toString(), taxExclusiveDifference.toString());
                log.error(message);
                return message;
            }
            
            // 记录调试信息
            log.debug("账单{}价格验证通过 - 含税总价差异: {}, 不含税总价差异: {}", 
                billKey, taxInclusiveDifference, taxExclusiveDifference);

            log.debug("账单{}校验通过", billKey);

            // 构建有效的账单对象
            ExpenseBill expenseBill = new ExpenseBill();
            expenseBill.setExpense_type(expenseType);
            expenseBill.setBilling_cycle(billingCycle);
            expenseBill.setTransfer_department(department);
            // 新增逻辑：如果已存在且为已退回，则设为已发布，否则为已入库
            if (existingBill != null && STATUS_RETURNED.equals(existingBill.getStatus())) {
                expenseBill.setStatus("1"); // 已发布
            } else {
                expenseBill.setStatus(STATUS_IMPORTED); // 已入库
            }
            expenseBill.setBill_publisher(receiveDTO.getMetadata().getPublisher());
            expenseBill.setTotal_price_with_tax(billTaxIncludingTotal);
            expenseBill.setTotal_price_without_tax(billTaxExcludingTotal);

            validBills.add(expenseBill);
            validExpenseDetails.add(validDetails);
        }

        log.info("所有账单校验通过，开始入库处理");

        // 3. 所有账单校验通过后，开始将本次的数据入库。如果有一份账单有误，本次的所有数据都不入库
        try {
            // 3.1 入库前再次批量检查所有账单是否存在重复且状态不允许覆盖
            log.info("执行入库前的批量重复账单检查");
            for (int i = 0; i < validBills.size(); i++) {
                ExpenseBill bill = validBills.get(i);
                String billKey = "账单" + (i + 1);
                
                log.debug("检查第{}份账单是否可入库：expenseType={}, billingCycle={}, department={}",
                    billKey, bill.getExpense_type(), bill.getBilling_cycle(), bill.getTransfer_department());
                
                ExpenseBill existingBill = selectExpenseBillByUniqueKey(
                    bill.getExpense_type(), bill.getBilling_cycle(), bill.getTransfer_department());
                
                if (existingBill != null) {
                    log.info("入库检查：发现已存在账单：bill_id={}, status={}, publisher={}", 
                        existingBill.getBill_id(), existingBill.getStatus(), existingBill.getBill_publisher());
                    
                    if (!STATUS_RETURNED.equals(existingBill.getStatus())) {
                        String departmentLabel = dictService.getLabel("expense_institude", bill.getTransfer_department());
                        String message = String.format("入库失败：%s(%s)已在系统中存在，且账单状态不为已退回状态，因此拒绝本次所有账单的入库操作。请联系系统管理员或者对应部门核对人退回账单。",
                                billKey, departmentLabel != null ? departmentLabel : bill.getTransfer_department());
                        log.error("批量入库检查失败：{}", message);
                        return message;
                    } else {
                        log.info("入库检查：账单{}({})已存在但状态为已退回，允许覆盖", billKey, 
                            dictService.getLabel("expense_institude", bill.getTransfer_department()));
                    }
                } else {
                    log.info("入库检查：账单{}({})不存在，可以新增", billKey, 
                        dictService.getLabel("expense_institude", bill.getTransfer_department()));
                }
            }
            log.info("批量重复账单检查通过，开始执行入库操作");
            
            // 3.2 执行实际的入库操作
            // 记录处理结果
            List<String> successDepartments = new ArrayList<>();

            for (int i = 0; i < validBills.size(); i++) {
                ExpenseBill bill = validBills.get(i);
                List<ExpenseDetails> details = validExpenseDetails.get(i);

                log.debug("处理第{}份账单：expenseType={}, billingCycle={}, department={}",
                        i + 1, bill.getExpense_type(), bill.getBilling_cycle(), bill.getTransfer_department());

                // 获取部门名称
                String departmentLabel = dictService.getLabel("expense_institude", bill.getTransfer_department());
                if (departmentLabel != null) {
                    successDepartments.add(departmentLabel);
                } else {
                    successDepartments.add(bill.getTransfer_department());
                }

                // 查询是否存在相同的账单（已退回状态）
                ExpenseBill existingBill = selectExpenseBillByUniqueKey(
                        bill.getExpense_type(), bill.getBilling_cycle(), bill.getTransfer_department());

                if (existingBill != null) {
                    // 更新已存在的账单
                    log.debug("更新已存在的账单：bill_id={}", existingBill.getBill_id());
                    bill.setBill_id(existingBill.getBill_id());
                    updateExpenseBill(bill);

                    // 删除旧的费用明细
                    log.debug("删除旧的费用明细：expenseType={}, billingCycle={}, transferDepartment={}",
                            existingBill.getExpense_type(), existingBill.getBilling_cycle(),
                            existingBill.getTransfer_department());
                    deleteExpenseDetailsByUnique(existingBill.getExpense_type(), existingBill.getBilling_cycle(),
                            existingBill.getTransfer_department());
                } else {
                    // 新增账单
                    log.debug("新增账单");
                    insertExpenseBill(bill);
                }

                // 新增费用明细
                log.debug("新增{}条费用明细", details.size());
                for (ExpenseDetails detail : details) {
                    expenseDetailsMapper.insertExpenseDetails(detail);
                }

                log.debug("第{}份账单处理完成", i + 1);
            }

            // 构建成功结果信息
            StringBuilder resultBuilder = new StringBuilder();
            resultBuilder.append("数据入库成功。共")
                         .append(validBills.size())
                         .append("份账单，包含以下机构：");
            
            // 添加所有部门名称
            resultBuilder.append(String.join("、", successDepartments));
            
            log.info("账单数据入库成功");
            return resultBuilder.toString();
        } catch (Exception e) {
            log.error("账单数据入库失败", e);
            throw e; // 抛出异常以触发事务回滚
        }
    }

    /**
     * 校验计费周期格式是否合法
     * 
     * @param billingCycle 计费周期
     * @return 是否合法
     */
    private boolean isValidBillingCycle(String billingCycle) {
        // 每月格式：202503
        if (billingCycle.matches("^\\d{6}$")) {
            return true;
        }

        // 其他周期格式：202504-06
        if (billingCycle.matches("^\\d{6}-\\d{2}$")) {
            return true;
        }

        return false;
    }

    /**
     * 删除指定费用类型、计费周期和划账部门的所有费用明细
     * 
     * @param expenseType        费用类型
     * @param billingCycle       计费周期
     * @param transferDepartment 划账部门
     */
    private void deleteExpenseDetailsByUnique(String expenseType, String billingCycle, String transferDepartment) {
        expenseDetailsMapper.deleteExpenseDetailsByUnique(expenseType, billingCycle, transferDepartment);
    }

    /**
     * 将费用明细DTO转换为实体对象
     * 
     * @param detail       费用明细DTO
     * @param expenseType  费用类型
     * @param billingCycle 计费周期
     * @param department   划账部门
     * @return 费用明细实体对象
     */
    private ExpenseDetails convertToExpenseDetails(ExpenseDetail detail, String expenseType, String billingCycle,
            String department) {
        if (detail == null) {
            throw new IllegalArgumentException("费用明细数据不能为空");
        }

        ExpenseDetails expenseDetails = new ExpenseDetails();
        expenseDetails.setName(detail.getExpenseName());
        expenseDetails.setNumber(detail.getNumber());
        expenseDetails.setBrand(detail.getBrand());
        expenseDetails.setSpecificSpecification(detail.getSpecification());
        expenseDetails.setExpenseType(expenseType);
        expenseDetails.setBillingCycle(billingCycle);
        expenseDetails.setTransferDepartment(department);
        expenseDetails.setQuantity(detail.getQuantity() != null ? detail.getQuantity().longValue() : null);
        expenseDetails.setExpenseChangeStatus(detail.getExpenseChangeInfo());
        expenseDetails.setRemarks(detail.getRemark());
        expenseDetails.setUnitPriceIncludingTax(detail.getTaxIncludingPrice());
        expenseDetails.setUnitPriceExcludingTax(detail.getTaxExcludingPrice());
        expenseDetails.setTotalLinePriceIncludingTax(detail.getTaxIncludingLineTotal());
        expenseDetails.setTotalLinePriceExcludingTax(detail.getTaxExcludingLineTotal());

        return expenseDetails;
    }

    /**
     * 校验账单详情结构是否与BillReceiveDTO一致
     * 
     * @param billDetail 账单详情
     * @param billKey    账单标识
     * @return 是否校验通过
     */
    private boolean validateBillDetailStructure(BillDetail billDetail, String billKey) {
        // 校验BillData结构
        BillData billData = billDetail.getBillData();
        if (billData == null) {
            log.error("账单{}的BillData为空", billKey);
            return false;
        }
        
        // 检查BillData必要字段
        if (billData.getExpenseType() == null || billData.getBillingCycle() == null || 
            billData.getDepartment() == null) {
            log.error("账单{}的BillData缺少必要字段", billKey);
            return false;
        }
        
        // 校验费用明细列表
        List<ExpenseDetail> details = billDetail.getExpenseDetails();
        if (details == null || details.isEmpty()) {
            log.error("账单{}的费用明细列表为空", billKey);
            return false;
        }
        
        // 校验每条费用明细的结构
        for (int i = 0; i < details.size(); i++) {
            ExpenseDetail detail = details.get(i);
            if (detail == null) {
                log.error("账单{}的第{}条费用明细为空", billKey, i + 1);
                return false;
            }
            
            // 检查所有字段是否存在（包括可以为空的字段）
            if (!validateExpenseDetailFields(detail)) {
                log.error("账单{}的第{}条费用明细缺少必要的字段结构", billKey, i + 1);
                return false;
            }
            
            //检查必填项是否为空
            if (StringUtils.isEmpty(detail.getExpenseName())) {
                log.error("账单{}的第{}条费用明细的[费用名称]为空", billKey, i + 1);
                return false;
            }
            if (StringUtils.isEmpty(detail.getBrand())) {
                log.error("账单{}的第{}条费用明细的[品牌]为空", billKey, i + 1);
                return false;
            }
            if (detail.getQuantity() == null) {
                log.error("账单{}的第{}条费用明细的[数量]为空", billKey, i + 1);
                return false;
            }
            if (detail.getTaxIncludingPrice() == null) {
                log.error("账单{}的第{}条费用明细的[含税单价]为空", billKey, i + 1);
                return false;
            }
            if (detail.getTaxExcludingPrice() == null) {
                log.error("账单{}的第{}条费用明细的[不含税单价]为空", billKey, i + 1);
                return false;
            }
            if (detail.getTaxIncludingLineTotal() == null) {
                log.error("账单{}的第{}条费用明细的[含税单行总价]为空", billKey, i + 1);
                return false;
            }
            if (detail.getTaxExcludingLineTotal() == null) {
                log.error("账单{}的第{}条费用明细的[不含税单行总价]为空", billKey, i + 1);
                return false;
            }
        }
        
        return true;
    }

    /**
     * 校验费用明细的字段结构完整性
     * 
     * @param detail 费用明细
     * @return 是否包含所有必要的字段结构
     */
    private boolean validateExpenseDetailFields(ExpenseDetail detail) {
        try {
            // 检查所有字段是否存在（字段可以为空，但结构必须存在）
            detail.getExpenseName();           // 费用名称
            detail.getNumber();                // 编号
            detail.getBrand();                 // 品牌
            detail.getSpecification();         // 具体规格
            detail.getQuantity();              // 数量
            detail.getExpenseChangeInfo();     // 费用变动情况
            detail.getRemark();                // 备注
            detail.getTaxIncludingPrice();     // 含税单价
            detail.getTaxExcludingPrice();     // 不含税单价
            detail.getTaxIncludingLineTotal(); // 含税单行总价
            detail.getTaxExcludingLineTotal(); // 不含税单行总价
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 校验数值字符串的小数点位数是否超过两位
     * 
     * @param value 数值字符串
     * @param fieldName 字段名称，用于错误信息
     * @return 校验错误信息，如果校验通过返回null
     */
    private String validateDecimalPlaces(String value, String fieldName) {
        if (StringUtils.isEmpty(value)) {
            return null; // 空值不校验小数点位数
        }
        
        try {
            BigDecimal decimal = new BigDecimal(value);
            // 获取小数点位数
            int scale = decimal.scale();
            if (scale > 2) {
                return String.format("%s的小数点位数为%d位，超过了最大允许的2位小数", fieldName, scale);
            }
        } catch (NumberFormatException e) {
            return String.format("%s的数值格式无效：%s", fieldName, value);
        }
        
        return null; // 校验通过
    }
    
    /**
     * 校验BigDecimal的小数点位数是否超过两位
     * 
     * @param value BigDecimal值
     * @param fieldName 字段名称，用于错误信息
     * @return 校验错误信息，如果校验通过返回null
     */
    private String validateDecimalPlaces(BigDecimal value, String fieldName) {
        if (value == null) {
            return null; // 空值不校验小数点位数
        }
        
        int scale = value.scale();
        if (scale > 2) {
            return String.format("%s的小数点位数为%d位，超过了最大允许的2位小数", fieldName, scale);
        }
        
        return null; // 校验通过
    }

    @Override
    public int clearReturnInfo(Long billId) {
        return expenseBillMapper.clearReturnInfo(billId);
    }

    @Override
    public void exportShareBillByParams(String expenseType, String billingCycle, HttpServletResponse response) throws Exception {
        if (StringUtils.isEmpty(expenseType) || StringUtils.isEmpty(billingCycle)) {
            writeErrorResponse(response, "费用类型和计费周期不能为空");
            return;
        }
        
        // 根据费用类型和计费周期查询符合条件的账单
        ExpenseBill queryBill = new ExpenseBill();
        queryBill.setExpense_type(expenseType);
        queryBill.setBilling_cycle(billingCycle);
        
        List<ExpenseBill> bills = selectExpenseBillList(queryBill);
        
        if (bills.isEmpty()) {
            writeErrorResponse(response, "未找到符合条件的账单数据");
            return;
        }
        
        // 生成Excel表格
        generateShareBillExcelWithExpenseCode(bills, response);
    }

    @Override
    public void exportShareBillByParamsWithResponsiblePerson(String expenseType, String billingCycle, String responsiblePerson, HttpServletResponse response) throws Exception {
        if (StringUtils.isEmpty(expenseType) || StringUtils.isEmpty(billingCycle)) {
            writeErrorResponse(response, "费用类型和计费周期不能为空");
            return;
        }
        
        // 根据费用类型和计费周期查询符合条件的账单
        ExpenseBill queryBill = new ExpenseBill();
        queryBill.setExpense_type(expenseType);
        queryBill.setBilling_cycle(billingCycle);
        
        List<ExpenseBill> bills = selectExpenseBillList(queryBill);
        
        if (bills.isEmpty()) {
            writeErrorResponse(response, "未找到符合条件的账单数据");
            return;
        }
        
        // 生成Excel表格（带负责人信息）
        generateShareBillExcelWithExpenseCodeAndResponsiblePerson(bills, responsiblePerson, response);
    }

    @Override
    public void exportShareBillByParamsWithResponsiblePersonAndTime(String expenseType, String billingCycle, String responsiblePerson, Date createTime, Date confirmTime, HttpServletResponse response) throws Exception {
        if (StringUtils.isEmpty(expenseType) || StringUtils.isEmpty(billingCycle)) {
            writeErrorResponse(response, "费用类型和计费周期不能为空");
            return;
        }

        // 根据费用类型和计费周期查询符合条件的账单
        ExpenseBill queryBill = new ExpenseBill();
        queryBill.setExpense_type(expenseType);
        queryBill.setBilling_cycle(billingCycle);

        List<ExpenseBill> bills = selectExpenseBillList(queryBill);

        if (bills.isEmpty()) {
            writeErrorResponse(response, "未找到符合条件的账单数据");
            return;
        }

        // 生成Excel表格（带负责人信息和时间信息）
        generateShareBillExcelWithExpenseCodeAndResponsiblePersonAndTime(bills, responsiblePerson, createTime, confirmTime, response);
    }

    @Override
    public void exportShareBillByParamsWithResponsiblePersonAndTimeAndTableName(String expenseType, String billingCycle, String responsiblePerson, Date createTime, Date confirmTime, String tableName, HttpServletResponse response) throws Exception {
        if (StringUtils.isEmpty(expenseType) || StringUtils.isEmpty(billingCycle)) {
            writeErrorResponse(response, "费用类型和计费周期不能为空");
            return;
        }

        // 根据费用类型和计费周期查询符合条件的账单
        ExpenseBill queryBill = new ExpenseBill();
        queryBill.setExpense_type(expenseType);
        queryBill.setBilling_cycle(billingCycle);

        List<ExpenseBill> bills = selectExpenseBillList(queryBill);

        if (bills.isEmpty()) {
            writeErrorResponse(response, "未找到符合条件的账单数据");
            return;
        }

        // 生成Excel表格（带负责人信息、时间信息和分摊表名称）
        generateShareBillExcelWithExpenseCodeAndResponsiblePersonAndTimeAndTableName(bills, responsiblePerson, createTime, confirmTime, tableName, response);
    }

    /**
     * 生成分摊表Excel（使用expense-code字典中的入账核算码，带负责人信息）
     *
     * @param bills 账单列表
     * @param responsiblePerson 负责人
     * @param response HTTP响应对象
     * @throws Exception 处理过程中的异常
     */
    private void generateShareBillExcelWithExpenseCodeAndResponsiblePerson(List<ExpenseBill> bills, String responsiblePerson, HttpServletResponse response) throws Exception {
        // 获取第一个账单的费用类型（假设同一次导出的账单都是同一费用类型）
        String expenseType = bills.isEmpty() ? "" : bills.get(0).getExpense_type();
        
        // 构建文件名：费用分摊表-yyyyMMdd
        String fileName = "费用分摊表-" + DateUtils.dateTimeNow("yyyyMMdd");
        
        // 创建工作簿
        Workbook wb = new SXSSFWorkbook(500);
        
        try {
            // 创建分摊表工作表
            Sheet sheet = wb.createSheet("分摊表");
            
            // 设置列宽
            sheet.setColumnWidth(0, 4000);  // 序号
            sheet.setColumnWidth(1, 6000);  // 管辖行/部门
            sheet.setColumnWidth(2, 6000);  // 分摊内容
            sheet.setColumnWidth(3, 5000);  // 入账核算码
            sheet.setColumnWidth(4, 6000);  // 金额（不含税）
            
            // 创建样式
            Map<String, CellStyle> styles = createExcelStyles(wb);
            
            // 创建表头和标题
            createExcelHeader(sheet, styles, expenseType);
            
            // 填充数据行
            int rowNum = 4;
            int serialNum = 1;
            BigDecimal totalAmount = BigDecimal.ZERO;
            
            // 获取入账核算码字典映射
            Map<String, String> codeMap = getExpenseCodeDictMap();
            
            for (ExpenseBill bill : bills) {
                Row dataRow = sheet.createRow(rowNum++);
                dataRow.setHeight((short) 400);
                
                // 序号
                Cell serialCell = dataRow.createCell(0);
                serialCell.setCellValue(serialNum++);
                serialCell.setCellStyle(styles.get("normal"));
                
                // 管辖行/部门
                Cell branchCell = dataRow.createCell(1);
                branchCell.setCellValue(bill.getTransfer_department());
                branchCell.setCellStyle(styles.get("normal"));
                
                // 分摊内容
                Cell contentCell = dataRow.createCell(2);
                contentCell.setCellValue(bill.getExpense_type());
                contentCell.setCellStyle(styles.get("normal"));
                
                // 入账核算码 - 从字典中获取
                Cell codeCell = dataRow.createCell(3);
                String expenseCode = codeMap.get(bill.getExpense_type());
                if (StringUtils.isEmpty(expenseCode)) {
                    expenseCode = "6576"; // 默认值
                }
                codeCell.setCellValue(expenseCode);
                codeCell.setCellStyle(styles.get("normal"));
                
                // 金额
                Cell amountCell = dataRow.createCell(4);
                BigDecimal amount = bill.getTotal_price_without_tax();
                if (amount != null) {
                    amountCell.setCellValue(amount.doubleValue());
                    totalAmount = totalAmount.add(amount);
                } else {
                    amountCell.setCellValue(0.00);
                }
                amountCell.setCellStyle(styles.get("number"));
            }
            
            // 获取制表人和复核人信息
            String billPublisher = bills.isEmpty() ? "" : bills.get(0).getBill_publisher();
            String reviewer = "";
            
            // 从分摊表记录中获取复核人信息
            try {
                String billingCycle = bills.isEmpty() ? "" : bills.get(0).getBilling_cycle();
                
                if (StringUtils.isNotEmpty(expenseType) && StringUtils.isNotEmpty(billingCycle)) {
                    com.ruoyi.expense.domain.ExpenseAllocationTable allocationTable = 
                        expenseAllocationTableMapper.selectByExpenseTypeAndBillingCycle(expenseType, billingCycle);
                    if (allocationTable != null && StringUtils.isNotEmpty(allocationTable.getReviewer())) {
                        reviewer = allocationTable.getReviewer();
                    }
                }
            } catch (Exception e) {
                log.warn("获取分摊表复核人信息失败", e);
            }
            
            // 如果从分摊表记录中获取不到复核人，则使用当前登录用户作为备选
            if (StringUtils.isEmpty(reviewer)) {
                try {
                    SysUser sysUser = ShiroUtils.getSysUser();
                    if (sysUser != null) {
                        reviewer = sysUser.getLoginName();
                    }
                } catch (Exception e) {
                    log.warn("获取当前用户信息失败", e);
                }
            }
            
            // 创建合计行和签名行（使用带负责人信息的版本）
            createSummaryRowsWithResponsiblePerson(sheet, styles, rowNum, totalAmount, 
                billPublisher, reviewer, responsiblePerson);
            
            // 输出Excel
            outputExcelToResponse(wb, fileName, response);
            
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e) {
                    log.error("关闭工作簿异常", e);
                }
            }
        }
    }

    /**
     * 生成分摊表Excel（使用expense-code字典中的入账核算码，带负责人信息和时间信息）
     *
     * @param bills 账单列表
     * @param responsiblePerson 负责人
     * @param createTime 创建时间
     * @param confirmTime 确认时间
     * @param response HTTP响应对象
     * @throws Exception 处理过程中的异常
     */
    private void generateShareBillExcelWithExpenseCodeAndResponsiblePersonAndTime(List<ExpenseBill> bills, String responsiblePerson, Date createTime, Date confirmTime, HttpServletResponse response) throws Exception {
        // 获取第一个账单的费用类型（假设同一次导出的账单都是同一费用类型）
        String expenseType = bills.isEmpty() ? "" : bills.get(0).getExpense_type();

        // 构建文件名：费用分摊表-yyyyMMdd
        String fileName = "费用分摊表-" + DateUtils.dateTimeNow("yyyyMMdd");

        // 创建工作簿
        Workbook wb = new SXSSFWorkbook(500);

        try {
            // 创建分摊表工作表
            Sheet sheet = wb.createSheet("分摊表");

            // 设置列宽
            sheet.setColumnWidth(0, 4000);  // 序号
            sheet.setColumnWidth(1, 6000);  // 管辖行/部门
            sheet.setColumnWidth(2, 6000);  // 分摊内容
            sheet.setColumnWidth(3, 5000);  // 入账核算码
            sheet.setColumnWidth(4, 6000);  // 金额（不含税）

            // 创建样式
            Map<String, CellStyle> styles = createExcelStyles(wb);

            // 创建表头和标题
            createExcelHeader(sheet, styles, expenseType, createTime);

            // 填充数据行
            int rowNum = 4;
            int serialNum = 1;
            BigDecimal totalAmount = BigDecimal.ZERO;

            // 获取入账核算码字典映射
            Map<String, String> codeMap = getExpenseCodeDictMap();

            for (ExpenseBill bill : bills) {
                Row row = sheet.createRow(rowNum++);

                // 序号
                Cell cell0 = row.createCell(0);
                cell0.setCellValue(serialNum++);
                cell0.setCellStyle(styles.get("data"));

                // 管辖行/部门
                Cell cell1 = row.createCell(1);
                cell1.setCellValue(bill.getTransfer_department());
                cell1.setCellStyle(styles.get("data"));

                // 分摊内容
                Cell cell2 = row.createCell(2);
                cell2.setCellValue(bill.getExpense_type());
                cell2.setCellStyle(styles.get("data"));

                // 入账核算码
                Cell cell3 = row.createCell(3);
                String expenseCode = codeMap.get(bill.getExpense_type());
                cell3.setCellValue(expenseCode != null ? expenseCode : "");
                cell3.setCellStyle(styles.get("data"));

                // 金额（不含税）
                Cell cell4 = row.createCell(4);
                BigDecimal amount = bill.getTotal_price_without_tax();
                if (amount != null) {
                    cell4.setCellValue(amount.doubleValue());
                    totalAmount = totalAmount.add(amount);
                } else {
                    cell4.setCellValue(0);
                }
                cell4.setCellStyle(styles.get("data"));
            }

            // 获取制表人和复核人信息
            String billPublisher = "";
            String reviewer = "";

            // 从分摊表记录中获取制表人和复核人信息
            if (!bills.isEmpty()) {
                String expenseType2 = bills.get(0).getExpense_type();
                String billingCycle = bills.get(0).getBilling_cycle();

                // 查询分摊表记录
                com.ruoyi.expense.domain.ExpenseAllocationTable allocationTable = expenseAllocationTableMapper.selectByExpenseTypeAndBillingCycle(expenseType2, billingCycle);
                if (allocationTable != null) {
                    billPublisher = allocationTable.getPreparer();
                    reviewer = allocationTable.getReviewer();
                }
            }

            // 如果从分摊表记录中获取不到制表人，则使用第一个账单的发布人
            if (StringUtils.isEmpty(billPublisher) && !bills.isEmpty()) {
                billPublisher = bills.get(0).getBill_publisher();
            }

            // 如果从分摊表记录中获取不到复核人，则使用当前登录用户作为备选
            if (StringUtils.isEmpty(reviewer)) {
                try {
                    SysUser sysUser = ShiroUtils.getSysUser();
                    if (sysUser != null) {
                        reviewer = sysUser.getLoginName();
                    }
                } catch (Exception e) {
                    log.warn("获取当前用户信息失败", e);
                }
            }

            // 创建合计行和签名行（使用带负责人信息和时间信息的版本）
            createSummaryRowsWithResponsiblePersonAndTime(sheet, styles, rowNum, totalAmount,
                billPublisher, reviewer, responsiblePerson, createTime, confirmTime);

            // 输出Excel文件
            outputExcelToResponse(wb, fileName, response);

        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e) {
                    log.error("关闭工作簿异常", e);
                }
            }
        }
    }

    /**
     * 生成分摊表Excel（使用expense-code字典中的入账核算码，带负责人信息、时间信息和分摊表名称）
     *
     * @param bills 账单列表
     * @param responsiblePerson 负责人
     * @param createTime 创建时间
     * @param confirmTime 确认时间
     * @param tableName 分摊表名称
     * @param response HTTP响应对象
     * @throws Exception 处理过程中的异常
     */
    private void generateShareBillExcelWithExpenseCodeAndResponsiblePersonAndTimeAndTableName(List<ExpenseBill> bills, String responsiblePerson, Date createTime, Date confirmTime, String tableName, HttpServletResponse response) throws Exception {
        // 获取第一个账单的费用类型（假设同一次导出的账单都是同一费用类型）
        String expenseType = bills.isEmpty() ? "" : bills.get(0).getExpense_type();

        // 使用分摊表名称作为文件名，如果为空则使用默认格式
        String fileName = StringUtils.isNotEmpty(tableName) ? tableName : "费用分摊表-" + DateUtils.dateTimeNow("yyyyMMdd");

        // 创建工作簿
        Workbook wb = new SXSSFWorkbook(500);

        try {
            // 创建分摊表工作表
            Sheet sheet = wb.createSheet("分摊表");

            // 设置列宽
            sheet.setColumnWidth(0, 4000);  // 序号
            sheet.setColumnWidth(1, 6000);  // 管辖行/部门
            sheet.setColumnWidth(2, 6000);  // 分摊内容
            sheet.setColumnWidth(3, 5000);  // 入账核算码
            sheet.setColumnWidth(4, 6000);  // 金额（不含税）

            // 创建样式
            Map<String, CellStyle> styles = createExcelStyles(wb);

            // 创建表头和标题
            createExcelHeader(sheet, styles, expenseType, createTime);

            // 填充数据行
            int rowNum = 4;
            int serialNum = 1;
            BigDecimal totalAmount = BigDecimal.ZERO;

            // 获取入账核算码字典映射
            Map<String, String> codeMap = getExpenseCodeDictMap();

            for (ExpenseBill bill : bills) {
                Row row = sheet.createRow(rowNum++);

                // 序号
                Cell cell0 = row.createCell(0);
                cell0.setCellValue(serialNum++);
                cell0.setCellStyle(styles.get("data"));

                // 管辖行/部门
                Cell cell1 = row.createCell(1);
                cell1.setCellValue(bill.getTransfer_department());
                cell1.setCellStyle(styles.get("data"));

                // 分摊内容
                Cell cell2 = row.createCell(2);
                cell2.setCellValue(bill.getExpense_type());
                cell2.setCellStyle(styles.get("data"));

                // 入账核算码
                Cell cell3 = row.createCell(3);
                String expenseCode = codeMap.get(bill.getExpense_type());
                cell3.setCellValue(expenseCode != null ? expenseCode : "");
                cell3.setCellStyle(styles.get("data"));

                // 金额（不含税）
                Cell cell4 = row.createCell(4);
                BigDecimal amount = bill.getTotal_price_without_tax();
                if (amount != null) {
                    cell4.setCellValue(amount.doubleValue());
                    totalAmount = totalAmount.add(amount);
                } else {
                    cell4.setCellValue(0);
                }
                cell4.setCellStyle(styles.get("data"));
            }

            // 获取制表人和复核人信息
            String billPublisher = "";
            String reviewer = "";

            // 从分摊表记录中获取制表人和复核人信息
            if (!bills.isEmpty()) {
                String expenseType2 = bills.get(0).getExpense_type();
                String billingCycle = bills.get(0).getBilling_cycle();

                // 查询分摊表记录
                com.ruoyi.expense.domain.ExpenseAllocationTable allocationTable = expenseAllocationTableMapper.selectByExpenseTypeAndBillingCycle(expenseType2, billingCycle);
                if (allocationTable != null) {
                    billPublisher = allocationTable.getPreparer();
                    reviewer = allocationTable.getReviewer();
                }
            }

            // 如果从分摊表记录中获取不到制表人，则使用第一个账单的发布人
            if (StringUtils.isEmpty(billPublisher) && !bills.isEmpty()) {
                billPublisher = bills.get(0).getBill_publisher();
            }

            // 如果从分摊表记录中获取不到复核人，则使用当前登录用户作为备选
            if (StringUtils.isEmpty(reviewer)) {
                try {
                    SysUser sysUser = ShiroUtils.getSysUser();
                    if (sysUser != null) {
                        reviewer = sysUser.getLoginName();
                    }
                } catch (Exception e) {
                    log.warn("获取当前用户信息失败", e);
                }
            }

            // 创建合计行和签名行（使用带负责人信息和时间信息的版本）
            createSummaryRowsWithResponsiblePersonAndTime(sheet, styles, rowNum, totalAmount,
                billPublisher, reviewer, responsiblePerson, createTime, confirmTime);

            // 输出Excel文件
            outputExcelToResponse(wb, fileName, response);

        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e) {
                    log.error("关闭工作簿异常", e);
                }
            }
        }
    }

    /**
     * 导出账单分摊表
     *
     * @param billIds 账单ID列表，多个ID用逗号分隔
     * @param response HTTP响应对象，用于输出文件流
     * @throws Exception 处理过程中的异常
     */
    @Override
    public void exportShareBill(String billIds, HttpServletResponse response) throws Exception {
        if (StringUtils.isEmpty(billIds)) {
            writeErrorResponse(response, "请选择要导出的账单");
            return;
        }
        
        // 解析账单ID列表
        String[] idArr = billIds.split(",");
        List<Long> idList = new ArrayList<>();
        for (String id : idArr) {
            try {
                idList.add(Long.parseLong(id));
            } catch (NumberFormatException e) {
                log.error("无效的账单ID: {}", id);
            }
        }
        
        if (idList.isEmpty()) {
            writeErrorResponse(response, "无有效的账单ID");
            return;
        }
        
        // 查询选中的账单
        List<ExpenseBill> bills = new ArrayList<>();
        for (Long id : idList) {
            ExpenseBill bill = selectExpenseBillById(id);
            if (bill != null) {
                bills.add(bill);
            }
        }
        
        if (bills.isEmpty()) {
            writeErrorResponse(response, "未找到有效的账单数据");
            return;
        }
        
        // 生成Excel表格
        generateShareBillExcel(bills, response);
    }

    /**
     * 生成分摊表Excel（使用expense-code字典中的入账核算码）
     *
     * @param bills 账单列表
     * @param response HTTP响应对象
     * @throws Exception 处理过程中的异常
     */
    private void generateShareBillExcelWithExpenseCode(List<ExpenseBill> bills, HttpServletResponse response) throws Exception {
        // 获取第一个账单的费用类型（假设同一次导出的账单都是同一费用类型）
        String expenseType = bills.isEmpty() ? "" : bills.get(0).getExpense_type();
        
        // 构建文件名：费用分摊表-yyyyMMdd
        String fileName = "费用分摊表-" + DateUtils.dateTimeNow("yyyyMMdd");
        
        // 创建工作簿
        Workbook wb = new SXSSFWorkbook(500);
        
        try {
            // 创建分摊表工作表
            Sheet sheet = wb.createSheet("分摊表");
            
            // 设置列宽
            sheet.setColumnWidth(0, 4000);  // 序号
            sheet.setColumnWidth(1, 6000);  // 管辖行/部门
            sheet.setColumnWidth(2, 6000);  // 分摊内容
            sheet.setColumnWidth(3, 5000);  // 入账核算码
            sheet.setColumnWidth(4, 6000);  // 金额（不含税）
            
            // 创建样式
            Map<String, CellStyle> styles = createExcelStyles(wb);
            
            // 创建表头和标题
            createExcelHeader(sheet, styles, expenseType);
            
            // 获取入账核算码字典数据
            Map<String, String> expenseCodeMap = getExpenseCodeDictMap();
            
            // 填充数据行
            int rowNum = 4;
            int serialNum = 1;
            BigDecimal totalAmount = BigDecimal.ZERO;
            
            for (ExpenseBill bill : bills) {
                Row dataRow = sheet.createRow(rowNum++);
                dataRow.setHeight((short) 400);
                
                // 序号
                Cell serialCell = dataRow.createCell(0);
                serialCell.setCellValue(serialNum++);
                serialCell.setCellStyle(styles.get("normal"));
                
                // 管辖行/部门
                Cell branchCell = dataRow.createCell(1);
                branchCell.setCellValue(bill.getTransfer_department());
                branchCell.setCellStyle(styles.get("normal"));
                
                // 分摊内容
                Cell contentCell = dataRow.createCell(2);
                contentCell.setCellValue(bill.getExpense_type());
                contentCell.setCellStyle(styles.get("normal"));
                
                // 入账核算码 - 从字典中获取
                Cell codeCell = dataRow.createCell(3);
                String expenseCode = expenseCodeMap.get(bill.getExpense_type());
                if (StringUtils.isEmpty(expenseCode)) {
                    expenseCode = "6576"; // 默认值
                }
                codeCell.setCellValue(expenseCode);
                codeCell.setCellStyle(styles.get("normal"));
                
                // 金额
                Cell amountCell = dataRow.createCell(4);
                BigDecimal amount = bill.getTotal_price_without_tax();
                if (amount != null) {
                    amountCell.setCellValue(amount.doubleValue());
                    totalAmount = totalAmount.add(amount);
                } else {
                    amountCell.setCellValue(0.00);
                }
                amountCell.setCellStyle(styles.get("number"));
            }
            
            // 获取制表人和复核人信息
            String billPublisher = bills.isEmpty() ? "" : bills.get(0).getBill_publisher();
            String reviewer = "";
            
            // 从分摊表记录中获取复核人信息
            try {
                String billingCycle = bills.isEmpty() ? "" : bills.get(0).getBilling_cycle();
                
                if (StringUtils.isNotEmpty(expenseType) && StringUtils.isNotEmpty(billingCycle)) {
                    com.ruoyi.expense.domain.ExpenseAllocationTable allocationTable = 
                        expenseAllocationTableMapper.selectByExpenseTypeAndBillingCycle(expenseType, billingCycle);
                    if (allocationTable != null && StringUtils.isNotEmpty(allocationTable.getReviewer())) {
                        reviewer = allocationTable.getReviewer();
                    }
                }
            } catch (Exception e) {
                log.warn("获取分摊表复核人信息失败", e);
            }
            
            // 如果从分摊表记录中获取不到复核人，则使用当前登录用户作为备选
            if (StringUtils.isEmpty(reviewer)) {
                try {
                    SysUser sysUser = ShiroUtils.getSysUser();
                    if (sysUser != null) {
                        reviewer = sysUser.getUserName();
                    }
                } catch (Exception e) {
                    log.warn("获取当前用户信息失败", e);
                }
            }
            
            // 创建合计行和签名行
            createSummaryRows(sheet, styles, rowNum, totalAmount, billPublisher, reviewer);
            
            // 保存文件并输出到响应
            outputExcelToResponse(wb, fileName, response);
            
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e) {
                    log.error("关闭工作簿异常{}", e.getMessage());
                }
            }
        }
    }

    /**
     * 获取入账核算码字典映射
     *
     * @return 费用类型到入账核算码的映射
     */
    private Map<String, String> getExpenseCodeDictMap() {
        Map<String, String> codeMap = new HashMap<>();
        try {
            // 从字典服务获取expense-code字典数据
            List<SysDictData> expenseCodeList = dictService.getType("expense-code");
            if (expenseCodeList != null && !expenseCodeList.isEmpty()) {
                for (SysDictData codeItem : expenseCodeList) {
                    String dictLabel = codeItem.getDictLabel();
                    String dictValue = codeItem.getDictValue();
                    if (StringUtils.isNotEmpty(dictLabel) && StringUtils.isNotEmpty(dictValue)) {
                        codeMap.put(dictLabel, dictValue);
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取入账核算码字典数据失败", e);
        }
        return codeMap;
    }
    
    /**
     * 生成分摊表Excel
     *
     * @param bills 账单列表
     * @param response HTTP响应对象
     * @throws Exception 处理过程中的异常
     */
    private void generateShareBillExcel(List<ExpenseBill> bills, HttpServletResponse response) throws Exception {
        // 获取第一个账单的费用类型（假设同一次导出的账单都是同一费用类型）
        String expenseType = bills.isEmpty() ? "" : bills.get(0).getExpense_type();
        
        // 构建文件名：费用分摊表-yyyyMMdd
        String fileName = "费用分摊表-" + DateUtils.dateTimeNow("yyyyMMdd");
        
        // 创建工作簿
        Workbook wb = new SXSSFWorkbook(500);
        
        try {
            // 创建分摊表工作表
            Sheet sheet = wb.createSheet("分摊表");
            
            // 设置列宽
            sheet.setColumnWidth(0, 4000);  // 序号
            sheet.setColumnWidth(1, 6000);  // 管辖行/部门
            sheet.setColumnWidth(2, 6000);  // 分摊内容
            sheet.setColumnWidth(3, 5000);  // 入账核算码
            sheet.setColumnWidth(4, 6000);  // 金额（不含税）
            
            // 创建样式
            Map<String, CellStyle> styles = createExcelStyles(wb);
            
            // 创建表头和标题
            createExcelHeader(sheet, styles, expenseType);
            
            // 填充数据行
            int rowNum = 4;
            int serialNum = 1;
            BigDecimal totalAmount = BigDecimal.ZERO;
            
            for (ExpenseBill bill : bills) {
                Row dataRow = sheet.createRow(rowNum++);
                dataRow.setHeight((short) 400);
                
                // 序号
                Cell serialCell = dataRow.createCell(0);
                serialCell.setCellValue(serialNum++);
                serialCell.setCellStyle(styles.get("normal"));
                
                // 管辖行/部门
                Cell branchCell = dataRow.createCell(1);
                branchCell.setCellValue(bill.getTransfer_department());
                branchCell.setCellStyle(styles.get("normal"));
                
                // 分摊内容
                Cell contentCell = dataRow.createCell(2);
                contentCell.setCellValue(bill.getExpense_type());
                contentCell.setCellStyle(styles.get("normal"));
                
                // 入账核算码
                Cell codeCell = dataRow.createCell(3);
                codeCell.setCellValue("6576");
                codeCell.setCellStyle(styles.get("normal"));
                
                // 金额
                Cell amountCell = dataRow.createCell(4);
                BigDecimal amount = bill.getTotal_price_without_tax();
                if (amount != null) {
                    amountCell.setCellValue(amount.doubleValue());
                    totalAmount = totalAmount.add(amount);
                } else {
                    amountCell.setCellValue(0.00);
                }
                amountCell.setCellStyle(styles.get("number"));
            }
            
            // 获取制表人和复核人信息
            String billPublisher = bills.isEmpty() ? "" : bills.get(0).getBill_publisher();
            String reviewer = "";
            
            // 从分摊表记录中获取复核人信息
            try {
                String billingCycle = bills.isEmpty() ? "" : bills.get(0).getBilling_cycle();
                
                if (StringUtils.isNotEmpty(expenseType) && StringUtils.isNotEmpty(billingCycle)) {
                    com.ruoyi.expense.domain.ExpenseAllocationTable allocationTable = 
                        expenseAllocationTableMapper.selectByExpenseTypeAndBillingCycle(expenseType, billingCycle);
                    if (allocationTable != null && StringUtils.isNotEmpty(allocationTable.getReviewer())) {
                        reviewer = allocationTable.getReviewer();
                    }
                }
            } catch (Exception e) {
                log.warn("获取分摊表复核人信息失败", e);
            }
            
            // 如果从分摊表记录中获取不到复核人，则使用当前登录用户作为备选
            if (StringUtils.isEmpty(reviewer)) {
                try {
                    SysUser sysUser = ShiroUtils.getSysUser();
                    if (sysUser != null) {
                        reviewer = sysUser.getUserName();
                    }
                } catch (Exception e) {
                    log.warn("获取当前用户信息失败", e);
                }
            }
            
            // 创建合计行和签名行
            createSummaryRows(sheet, styles, rowNum, totalAmount, billPublisher, reviewer);
            
            // 保存文件并输出到响应
            outputExcelToResponse(wb, fileName, response);
            
        } finally {
            if (wb != null) {
                try {
                    wb.close();
                } catch (IOException e) {
                    log.error("关闭工作簿异常{}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 创建Excel样式
     *
     * @param wb 工作簿
     * @return 样式映射
     */
    private Map<String, CellStyle> createExcelStyles(Workbook wb) {
        Map<String, CellStyle> styles = new HashMap<>();

        // 标题样式
        CellStyle titleStyle = wb.createCellStyle();
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        Font titleFont = wb.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 16);
        titleStyle.setFont(titleFont);
        styles.put("title", titleStyle);

        // 表头样式
        CellStyle headerStyle = wb.createCellStyle();
        headerStyle.setAlignment(HorizontalAlignment.CENTER);
        headerStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        headerStyle.setBorderTop(BorderStyle.THIN);
        headerStyle.setBorderBottom(BorderStyle.THIN);
        headerStyle.setBorderLeft(BorderStyle.THIN);
        headerStyle.setBorderRight(BorderStyle.THIN);
        Font headerFont = wb.createFont();
        headerFont.setBold(true);
        headerFont.setFontHeightInPoints((short) 12);
        headerStyle.setFont(headerFont);
        styles.put("header", headerStyle);

        // 普通单元格样式
        CellStyle normalStyle = wb.createCellStyle();
        normalStyle.setAlignment(HorizontalAlignment.CENTER);
        normalStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        normalStyle.setBorderTop(BorderStyle.THIN);
        normalStyle.setBorderBottom(BorderStyle.THIN);
        normalStyle.setBorderLeft(BorderStyle.THIN);
        normalStyle.setBorderRight(BorderStyle.THIN);
        styles.put("normal", normalStyle);

        // 数据单元格样式（与normal样式相同，确保账单行有边框）
        CellStyle dataStyle = wb.createCellStyle();
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);
        styles.put("data", dataStyle);

        // 数字单元格样式
        CellStyle numberStyle = wb.createCellStyle();
        numberStyle.setAlignment(HorizontalAlignment.RIGHT);
        numberStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        numberStyle.setBorderTop(BorderStyle.THIN);
        numberStyle.setBorderBottom(BorderStyle.THIN);
        numberStyle.setBorderLeft(BorderStyle.THIN);
        numberStyle.setBorderRight(BorderStyle.THIN);
        DataFormat format = wb.createDataFormat();
        numberStyle.setDataFormat(format.getFormat("#,##0.00"));
        styles.put("number", numberStyle);

        // 合计标签样式
        CellStyle totalLabelStyle = wb.createCellStyle();
        totalLabelStyle.setAlignment(HorizontalAlignment.CENTER);
        totalLabelStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        totalLabelStyle.setBorderTop(BorderStyle.THIN);
        totalLabelStyle.setBorderBottom(BorderStyle.THIN);
        totalLabelStyle.setBorderLeft(BorderStyle.THIN);
        totalLabelStyle.setBorderRight(BorderStyle.THIN);
        Font totalFont = wb.createFont();
        totalFont.setBold(true);
        totalFont.setFontHeightInPoints((short) 12);
        totalLabelStyle.setFont(totalFont);
        styles.put("totalLabel", totalLabelStyle);

        return styles;
    }
    
    /**
     * 创建Excel表头和标题
     *
     * @param sheet 工作表
     * @param styles 样式映射
     * @param expenseType 费用类型
     */
    private void createExcelHeader(Sheet sheet, Map<String, CellStyle> styles, String expenseType) {
        createExcelHeader(sheet, styles, expenseType, null);
    }

    private void createExcelHeader(Sheet sheet, Map<String, CellStyle> styles, String expenseType, Date createTime) {
        // 获取费用类型显示名称
        String expenseTypeName = dictService.getLabel(EXPENSE_TYPE_DICT, expenseType);
        if (StringUtils.isEmpty(expenseTypeName)) {
            expenseTypeName = expenseType;
        }
        
        // 标题行 - 【费用类型】费用分摊表
        Row titleRow = sheet.createRow(0);
        titleRow.setHeight((short) 800);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(String.format("【%s】费用分摊表", expenseTypeName));
        titleCell.setCellStyle(styles.get("title"));
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 4));
        
        // 分摊部门行
        Row deptRow = sheet.createRow(1);
        deptRow.setHeight((short) 500);
        Cell deptLabelCell = deptRow.createCell(0);
        deptLabelCell.setCellValue("分摊部门：");
        Cell deptValueCell = deptRow.createCell(1);
        deptValueCell.setCellValue("金融科技部");  // 这里是固定的分摊部门
        Cell timeCell = deptRow.createCell(3);
        timeCell.setCellValue("分摊时间：");
        Cell timeValueCell = deptRow.createCell(4);
        
        // 使用分摊表的创建时间，如果为空则使用当前日期
        if (createTime != null) {
            timeValueCell.setCellValue(DateUtils.parseDateToStr("yyyy-MM-dd", createTime));
        } else {
            timeValueCell.setCellValue(DateUtils.getDate());
        }
        
        // 表头行
        Row headerRow = sheet.createRow(3);
        headerRow.setHeight((short) 500);
        String[] headers = {"序号", "管辖行/部门", "分摊内容", "入账核算码", "金额（不含税）"};
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(styles.get("header"));
        }
    }
    
    /**
     * 创建合计行和签名行
     *
     * @param sheet 工作表
     * @param styles 样式映射
     * @param rowNum 当前行号
     * @param totalAmount 总金额
     * @param billPublisher 制表人（账单上传人）
     * @param currentUser 复核人（当前导出用户）
     */
    private void createSummaryRows(Sheet sheet, Map<String, CellStyle> styles, int rowNum, BigDecimal totalAmount, String billPublisher, String currentUser) {
        // 合计行（直接在最后一个数据行后面，不留空行）
        Row totalRow = sheet.createRow(rowNum);
        totalRow.setHeight((short) 500);
        Cell totalLabelCell = totalRow.createCell(0);
        totalLabelCell.setCellValue("合计");
        totalLabelCell.setCellStyle(styles.get("totalLabel"));

        // 为合并区域的每个单元格都设置边框，解决合并单元格边框问题
        for (int i = 1; i <= 3; i++) {
            Cell cell = totalRow.createCell(i);
            cell.setCellStyle(styles.get("totalLabel"));
        }

        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 3));

        Cell totalAmountCell = totalRow.createCell(4);
        totalAmountCell.setCellValue(totalAmount.doubleValue());
        totalAmountCell.setCellStyle(styles.get("number"));

        // 已通知相关分支行或部门
        Row notifiedRow = sheet.createRow(rowNum + 2);
        Cell notifiedCell = notifiedRow.createCell(0);
        notifiedCell.setCellValue("已通知相关分支行或部门");

        // 制表人、复核人、负责人行
        Row signRow = sheet.createRow(rowNum + 4);
        signRow.createCell(0).setCellValue("制表人：" + (StringUtils.isNotEmpty(billPublisher) ? billPublisher : ""));
        signRow.createCell(2).setCellValue("复核人：" + (StringUtils.isNotEmpty(currentUser) ? currentUser : ""));
        signRow.createCell(4).setCellValue("负责人：");
    }

    /**
     * 创建合计行和签名行（带负责人信息）
     *
     * @param sheet 工作表
     * @param styles 样式映射
     * @param rowNum 当前行号
     * @param totalAmount 总金额
     * @param billPublisher 制表人（账单上传人）
     * @param currentUser 复核人（当前导出用户）
     * @param responsiblePerson 负责人
     */
    private void createSummaryRowsWithResponsiblePerson(Sheet sheet, Map<String, CellStyle> styles, int rowNum, BigDecimal totalAmount, String billPublisher, String currentUser, String responsiblePerson) {
        // 合计行（直接在最后一个数据行后面，不留空行）
        Row totalRow = sheet.createRow(rowNum);
        totalRow.setHeight((short) 500);
        Cell totalLabelCell = totalRow.createCell(0);
        totalLabelCell.setCellValue("合计");
        totalLabelCell.setCellStyle(styles.get("totalLabel"));

        // 为合并区域的每个单元格都设置边框，解决合并单元格边框问题
        for (int i = 1; i <= 3; i++) {
            Cell cell = totalRow.createCell(i);
            cell.setCellStyle(styles.get("totalLabel"));
        }

        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 3));

        Cell totalAmountCell = totalRow.createCell(4);
        totalAmountCell.setCellValue(totalAmount.doubleValue());
        totalAmountCell.setCellStyle(styles.get("number"));

        // 已通知被分摊行（部）行
        Row notifiedRow = sheet.createRow(rowNum + 2);
        Cell notifiedCell = notifiedRow.createCell(0);
        notifiedCell.setCellValue("已通知被分摊行（部）");

        // 制表人、复核人、负责人行
        Row signRow = sheet.createRow(rowNum + 4);
        signRow.createCell(0).setCellValue("制表人：" + (StringUtils.isNotEmpty(billPublisher) ? billPublisher : ""));
        signRow.createCell(2).setCellValue("复核人：" + (StringUtils.isNotEmpty(currentUser) ? currentUser : ""));
        signRow.createCell(4).setCellValue("负责人：" + (StringUtils.isNotEmpty(responsiblePerson) ? responsiblePerson : ""));
    }

    /**
     * 创建合计行和签名行（带负责人信息和时间信息）
     *
     * @param sheet 工作表
     * @param styles 样式映射
     * @param rowNum 当前行号
     * @param totalAmount 总金额
     * @param billPublisher 制表人（账单上传人）
     * @param currentUser 复核人（当前导出用户）
     * @param responsiblePerson 负责人
     * @param createTime 创建时间
     * @param confirmTime 确认时间
     */
    private void createSummaryRowsWithResponsiblePersonAndTime(Sheet sheet, Map<String, CellStyle> styles, int rowNum, BigDecimal totalAmount, String billPublisher, String currentUser, String responsiblePerson, Date createTime, Date confirmTime) {
        // 合计行（直接在最后一个数据行后面，不留空行）
        Row totalRow = sheet.createRow(rowNum);
        totalRow.setHeight((short) 500);
        Cell totalLabelCell = totalRow.createCell(0);
        totalLabelCell.setCellValue("合计");
        totalLabelCell.setCellStyle(styles.get("totalLabel"));
        
        // 为合并区域的每个单元格都设置边框，解决合并单元格边框问题
        for (int i = 1; i <= 3; i++) {
            Cell cell = totalRow.createCell(i);
            cell.setCellStyle(styles.get("totalLabel"));
        }
        
        sheet.addMergedRegion(new CellRangeAddress(rowNum, rowNum, 0, 3));
        
        Cell totalAmountCell = totalRow.createCell(4);
        totalAmountCell.setCellValue(totalAmount.doubleValue());
        totalAmountCell.setCellStyle(styles.get("number"));
        
        // 已通知被分摊行（部）行
        Row notifiedRow = sheet.createRow(rowNum + 2);
        Cell notifiedCell = notifiedRow.createCell(0);
        notifiedCell.setCellValue("已通知被分摊行（部）");
        
        // 删除“分摊时间：”这一行，直接跳过rowNum+3这一行
        // 制表人、复核人、负责人行
        Row signRow = sheet.createRow(rowNum + 4);
        signRow.createCell(0).setCellValue("制表人：" + (StringUtils.isNotEmpty(billPublisher) ? billPublisher : ""));
        signRow.createCell(2).setCellValue("复核人：" + (StringUtils.isNotEmpty(currentUser) ? currentUser : ""));
        signRow.createCell(4).setCellValue("负责人：" + (StringUtils.isNotEmpty(responsiblePerson) ? responsiblePerson : ""));
        
        // 确认时间单元格，调整至“负责人”正下方（即第6行的第4列，rowNum+6, cell 4 -> rowNum+6, cell 4 改为 rowNum+6, cell 4）
        Row confirmTimeRow = sheet.createRow(rowNum + 6);
        Cell confirmTimeCell = confirmTimeRow.createCell(4);
        if (confirmTime != null) {
            // 格式化时间，只保留年月日
            String confirmTimeStr = DateUtils.parseDateToStr("yyyy-MM-dd", confirmTime);
            confirmTimeCell.setCellValue("确认时间：" + confirmTimeStr);
        } else {
            confirmTimeCell.setCellValue("确认时间：");
        }
    }
    
    /**
     * 输出Excel到HTTP响应
     *
     * @param wb 工作簿
     * @param fileName 文件名
     * @param response HTTP响应对象
     * @throws IOException IO异常
     */
    private void outputExcelToResponse(Workbook wb, String fileName, HttpServletResponse response) throws IOException {
        String filename = encodingFilename(fileName);
        FileOutputStream out = null;
        
        try {
            String filePath = getAbsoluteFile(filename);
            out = new FileOutputStream(filePath);
            wb.write(out);
            
            // 返回自定义响应，不使用通用下载处理
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, fileName + ".xlsx");
            FileUtils.writeBytes(filePath, response.getOutputStream());
            FileUtils.deleteFile(filePath);
        } finally {
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("关闭输出流异常{}", e.getMessage());
                }
            }
        }
    }
    
    /**
     * 编码文件名
     */
    private String encodingFilename(String filename) {
        return filename + "_" + System.currentTimeMillis() + ".xlsx";
    }
    
    /**
     * 获取下载路径
     */
    private String getAbsoluteFile(String filename) {
        String downloadPath = RuoYiConfig.getDownloadPath() + filename;
        File desc = new File(downloadPath);
        if (!desc.getParentFile().exists()) {
            desc.getParentFile().mkdirs();
        }
        return downloadPath;
    }
    
    /**
     * 写入错误响应
     */
    private void writeErrorResponse(HttpServletResponse response, String message) throws IOException {
        response.setContentType("application/json");
        response.setCharacterEncoding("utf-8");
        response.getWriter().print("{\"code\":500,\"msg\":\"" + message + "\"}");
    }
}